#!/bin/bash

# 基于参考文件的完整参数测试脚本

echo "=== 基于参考文件的Spiralize模式Raft优化测试 ==="

# 测试Spiralize模式 - 使用参考文件中的完整参数
echo "测试: Spiralize模式 + Raft + bottom_layers=0"
./CuraEngine slice -v \
-s initial_extruder_nr="0" \
-s date="25-07-2025" \
-s print_temperature="210" \
-s material_name="empty" \
-s jerk_flooring="20" \
-s smooth_spiralized_contours="False" \
-s support_interface_line_width="0.8" \
-s z_seam_y="500.0" \
-s lightning_infill_overhang_angle="40" \
-s draft_shield_enabled="False" \
-s support_tree_tip_diameter="1.6" \
-s infill_offset_x="0" \
-s support_bottom_wall_count="0" \
-s minimum_interface_area="1.0" \
-s print_bed_temperature="80" \
-s lightning_infill_support_angle="40" \
-s wall_x_material_flow_roofing="100" \
-s brim_smart_ordering="True" \
-s bridge_settings_enabled="False" \
-s retraction_retract_speed="25" \
-s meshfix_fluid_motion_angle="15" \
-s coasting_min_volume="0.8" \
-s gradual_infill_step_height="1.5" \
-s support_pattern="zigzag" \
-s speed_wall_x="60.0" \
-s cool_min_layer_time="5" \
-s support_bottom_line_distance="0.8" \
-s jerk_wall_x="20" \
-s top_bottom="0" \
-s prime_tower_enable="False" \
-s jerk_roofing="20" \
-s material_final_print_temperature="195" \
-s support_brim_width="2.4000000000000004" \
-s z_seam_point_interpolation="False" \
-s machine_show_variants="False" \
-s support_interface_density="100" \
-s resolution="0" \
-s wall0_extra_length="0" \
-s quality_name="Draft" \
-s wall_0_material_flow_layer_0="100" \
-s wall_0_start_speed_ratio="100.0" \
-s draft_shield_height_limitation="full" \
-s support_extruder_nr="0" \
-s only_spiralize_out_surface="True" \
-s support_roof_line_width="0.8" \
-s travel="0" \
-s acceleration_wall_0="3000" \
-s retraction_hop_only_when_collides="False" \
-s build_volume_fan_speed="100" \
-s wipe_hop_speed="10" \
-s raft_interface_infill_overlap_mm="0.0" \
-s support_structure="normal" \
-s material_no_load_move_factor="0.*********" \
-s material_max_flowrate="16" \
-s cool_min_layer_time_fan_speed_max="10" \
-s infill_support_angle="40" \
-s user_speed_ratio_definition="[]" \
-s acceleration_print_layer_0="3000" \
-s day="Fri" \
-s wipe_retraction_enable="True" \
-s infill_angles="[ ]" \
-s acceleration_support_interface="3000" \
-s support_skip_zag_per_mm="20" \
-s top_bottom_pattern_0="lines" \
-s wall_transition_angle="10" \
-s jerk_support_infill="20" \
-s machine_endstop_positive_direction_z="True" \
-s coasting_volume="0.064" \
-s machine_depth="1000.0" \
-s material_bed_temp_wait="True" \
-s support_interface_height="1" \
-s top_bottom_thickness="0.8" \
-s extruder_prime_pos_y="0" \
-s speed_wall_x_roofing="60.0" \
-s roofing_extruder_nr="-1" \
-s raft_surface_smoothing="5" \
-s adaptive_layer_mini_flow_thickness="0.35" \
-s support_bottom_stair_step_height="0.3" \
-s top_bottom_pattern="lines" \
-s cool_min_layer_time_overhang="5" \
-s roofing_pattern="lines" \
-s acceleration_support_infill="3000" \
-s brim_gap="0" \
-s wall_0_extruder_nr="-1" \
-s support_tree_min_height_to_model="3" \
-s speed_wall_0_flooring="30.0" \
-s clean_between_layers="False" \
-s jerk_layer_0="20" \
-s cool_min_temperature="210" \
-s sub_div_rad_add="0.4" \
-s machine_max_acceleration_y="9000" \
-s support_bottom_line_width="0.8" \
-s ironing_only_highest_layer="False" \
-s raft_surface_remove_inside_corners="False" \
-s lightning_infill_prune_angle="40" \
-s skin_material_flow="100" \
-s support_bottom_density="100" \
-s gradual_support_infill_step_height="1" \
-s material_print_temperature_layer_0="210" \
-s jerk_topbottom="20" \
-s remove_empty_first_layers="True" \
-s speed_support_roof="50.0" \
-s roofing_angles="[ ]" \
-s retraction_after_wipe="False" \
-s infill_offset_y="0" \
-s roofing_layer_count="0" \
-s retraction_min_travel="0.8" \
-s raft_base_acceleration="3000" \
-s wall_extruder_nr="-1" \
-s ppr_enable="False" \
-s jerk_enabled="False" \
-s material_end_of_filament_purge_length="20" \
-s material_break_preparation_speed="2" \
-s material_alternate_walls="False" \
-s acceleration_support_bottom="3000" \
-s xy_offset="0" \
-s wall_transition_filter_distance="100" \
-s mesh_position_y="0" \
-s extruders_enabled_count="1" \
-s small_skin_on_surface="False" \
-s raft_interface_line_width="1.6" \
-s gradual_support_infill_steps="0" \
-s max_flow_acceleration="1" \
-s raft_surface_line_spacing="0.8" \
-s wall_line_width="0.4" \
-s wall_0_wipe_dist="0.2" \
-s material_break_retracted_position="-50" \
-s material_initial_print_temperature="200" \
-s roofing_material_flow="100" \
-s wipe_retraction_retract_speed="25" \
-s multiple_mesh_overlap="0.15" \
-s magic_spiralize="True" \
-s bridge_wall_coast="100" \
-s command_line_settings="0" \
-s support_angle="50" \
-s raft_base_speed="28.125" \
-s top_bottom_extruder_nr="-1" \
-s material_standby_temperature="150" \
-s raft_margin="4" \
-s brim_location="outside" \
-s machine_max_jerk_e="5.0" \
-s minimum_polygon_circumference="1.0" \
-s support_infill_rate="15" \
-s inset_direction="inside_out" \
-s raft_surface_flow="100.0" \
-s acceleration_print="3000" \
-s raft_surface_infill_overlap_mm="0.0" \
-s infill_sparse_thickness="0.3" \
-s prime_during_travel_ratio="0" \
-s speed_support_bottom="50.0" \
-s machine_minimum_feedrate="0.0" \
-s scarf_split_distance="1.0" \
-s machine_feeder_wheel_diameter="10.0" \
-s adaptive_layer_max_width_ratio="1.5" \
-s layer_start_y="0.0" \
-s build_volume_temperature="36" \
-s extruder_prime_pos_x="0" \
-s small_feature_speed_factor_0="50" \
-s magic_fuzzy_skin_enabled="False" \
-s bridge_skin_material_flow="60" \
-s speed_travel="60" \
-s raft_surface_infill_overlap="0" \
-s magic_mesh_surface_mode="normal" \
-s gradual_infill_steps="0" \
-s raft_surface_wall_count="0" \
-s support_meshes_present="False" \
-s travel_avoid_distance="0.625" \
-s raft_interface_speed="28.125" \
-s machine_scan_first_layer="False" \
-s ironing_monotonic="False" \
-s layer_height_0="0.3" \
-s support_roof_density="100" \
-s acceleration_wall_x="3000" \
-s flow_rate_extrusion_offset_factor="100" \
-s carve_multiple_volumes="False" \
-s machine_max_feedrate_y="************" \
-s raft_interface_z_offset="0.0" \
-s initial_layer_line_width_factor="100.0" \
-s machine_start_gcode="G28 ;Home
G1 Z15.0 F6000 ;Move the platform down 15mm
;Prime the extruder
G92 E0
G1 F200 E3
G92 E0" \
-s flow_anomaly_limit="25.0" \
-s prime_tower_brim_enable="True" \
-s flooring_extruder_nr="-1" \
-s raft_base_wall_count="1" \
-s acceleration_prime_tower="3000" \
-s print_temp_anomaly_limit="7.0" \
-s xy_offset_layer_0="0" \
-s bridge_skin_density="100" \
-s jerk_wall="20" \
-s speed_prime_tower="60" \
-s z_seam_type="sharpest_corner" \
-s retraction_prime_speed="25" \
-s meshfix_maximum_travel_resolution="0.5" \
-s support_extruder_nr_layer_0="0" \
-s ironing_line_spacing="0.1" \
-s cool_during_extruder_switch="unchanged" \
-s gradual_flow_discretisation_step_size="0.2" \
-s speed_ironing="20.0" \
-s ppr="0" \
-s raft_fan_speed="0" \
-s jerk_support_interface="20" \
-s coasting_enable="False" \
-s support_tower_diameter="3.0" \
-s support_conical_angle="30" \
-s meshfix_maximum_deviation="0.025" \
-s prime_tower_max_bridging_distance="5" \
-s magic_fuzzy_skin_point_density="1.25" \
-s prime_tower_mode="interleaved" \
-s mold_enabled="False" \
-s jerk_support_bottom="20" \
-s raft_surface_line_width="0.8" \
-s bv_temp_anomaly_limit="10.0" \
-s roofing_line_width="0.4" \
-s support="0" \
-s infill_mesh_order="0" \
-s min_wall_line_width="0.34" \
-s raft_base_line_width="1.2" \
-s raft_base_margin="4" \
-s infill_randomize_start_location="False" \
-s machine_steps_per_mm_y="50" \
-s support_tree_branch_diameter="5" \
-s raft_wall_count="1" \
-s raft_base_line_spacing="2.4" \
-s max_extrusion_before_wipe="10" \
-s speed_support_infill="75" \
-s support_infill_angles="[ ]" \
-s small_skin_width="0.8" \
-s shell="0" \
-s flooring_pattern="lines" \
-s top_layers="3" \
-s raft_interface_remove_inside_corners="False" \
-s layer_start_x="0.0" \
-s machine_min_cool_heat_time_window="50.0" \
-s cooling="0" \
-s support_bottom_distance="0.1" \
-s wall_thickness="0.4" \
-s machine_steps_per_mm_e="1600" \
-s material_crystallinity="False" \
-s infill_overlap_mm="0.04" \
-s conical_overhang_angle="50" \
-s mesh_position_z="0" \
-s support_tree_limit_branch_reach="True" \
-s skin_no_small_gaps_heuristic="False" \
-s cross_support_density_image="" \
-s z_seam_relative="False" \
-s support_roof_pattern="concentric" \
-s support_mesh="False" \
-s machine_width="1000.0" \
-s machine_max_feedrate_x="************" \
-s user_thickness_definition="[0.2,0.2][5,0.2][30,0.4][50,0.4][90,0.2][110,0.3][120,0.4][150,0.202]" \
-s adaptive_layer_height_threshold="0.45" \
-s raft_interface_acceleration="3000" \
-s raft_base_infill_overlap_mm="0.0" \
-s support_infill_sparse_thickness="0.3" \
-s material_bed_temperature_layer_0="80" \
-s build_volume_fan_speed_0="0" \
-s draft_shield_dist="10" \
-s material_flush_purge_length="60" \
-s machine_nozzle_heat_up_speed="2.0" \
-s raft_interface_line_spacing="1.8" \
-s machine_heated_build_volume="False" \
-s skin_edge_support_thickness="0" \
-s support_wall_count="0" \
-s machine_max_acceleration_z="100" \
-s build_fan_full_at_height="0" \
-s mesh_rotation_matrix="[[1,0,0], [0,1,0], [0,0,1]]" \
-s cool_fan_speed_max="100.0" \
-s acceleration_wall_0_flooring="3000" \
-s cool_fan_enabled="True" \
-s wall_overhang_angle="90" \
-s support_supported_skin_fan_speed="100" \
-s infill_wall_line_count="0" \
-s magic_fuzzy_skin_thickness="0.3" \
-s scarf_joint_seam_length="0" \
-s meshfix_extensive_stitching="False" \
-s bottom_layers="3" \
-s print_temp_warn_limit="3.0" \
-s acceleration_travel="3000" \
-s raft_base_extruder_nr="0" \
-s support_bottom_material_flow="100" \
-s ironing_enabled="False" \
-s acceleration_wall="3000" \
-s material_flow="100" \
-s support_offset="1.2000000000000002" \
-s support_interface_wall_count="0" \
-s raft_interface_jerk="20" \
-s material_shrinkage_percentage="100.0" \
-s interlocking_depth="2" \
-s draw_z_seam_points="[]" \
-s support_roof_angles="[ ]" \
-s material_print_temp_wait="True" \
-s machine_max_jerk_xy="20.0" \
-s cutting_mesh="False" \
-s material_print_temperature="210" \
-s min_even_wall_line_width="0.34" \
-s interlocking_boundary_avoidance="2" \
-s support_roof_material_flow="100" \
-s raft_interface_infill_overlap="0" \
-s switch_extruder_prime_speed="20" \
-s min_bead_width="0.34" \
-s wall_x_extruder_nr="-1" \
-s connect_out_walls="False" \
-s support_tree_angle_slow="33.333333333333336" \
-s minimum_support_area="0.0" \
-s build_volume_fan_nr="0" \
-s extra_infill_lines_to_support_skins="walls_and_lines" \
-s prime_tower_min_shell_thickness="0.8" \
-s support_bottom_angles="[ ]" \
-s support_tree_branch_reach_limit="30" \
-s infill_multiplier="1" \
-s support_roof_offset="0.0" \
-s acceleration_support_roof="3000" \
-s bridge_skin_density_3="80" \
-s support_interface_enable="False" \
-s alternate_extra_perimeter="False" \
-s support_roof_enable="False" \
-s skin_material_flow_layer_0="100" \
-s ooze_shield_angle="60" \
-s machine_endstop_positive_direction_x="False" \
-s support_use_towers="True" \
-s acceleration_wall_x_roofing="3000" \
-s adaptive_layer_height_variation="0.2" \
-s raft_interface_layers="1" \
-s cross_infill_density_image="" \
-s speed_support="75" \
-s ironing_inset="0.38" \
-s bridge_fan_speed_3="0" \
-s raft_surface_fan_speed="0" \
-s material_extrusion_cool_down_speed="0.7" \
-s acceleration_wall_0_roofing="3000" \
-s material_flush_purge_speed="0.5" \
-s wall_distribution_count="1" \
-s raft_airgap="0.1" \
-s support_skip_some_zags="False" \
-s support_line_width="0.8" \
-s ooze_shield_enabled="False" \
-s retraction_during_travel_ratio="0" \
-s wall_line_count="1" \
-s speed_z_hop="10" \
-s adhesion_extruder_nr="0" \
-s mold_width="5" \
-s user_defined_print_order_enabled="False" \
-s alternate_carve_order="True" \
-s machine_firmware_retract="False" \
-s raft_base_infill_overlap="0" \
-s switch_extruder_retraction_speeds="20" \
-s adaptive_layer_height_variation_step="0.002" \
-s raft_surface_jerk="20" \
-s speed_print="60" \
-s speed_flooring="30.0" \
-s min_skin_width_for_expansion="5.510910596163089e-17" \
-s flooring_line_width="0.4" \
-s keep_retracting_during_travel="False" \
-s skirt_brim_extruder_nr="0" \
-s user_thickness_definition_enable="False" \
-s ooze_shield_dist="2" \
-s travel_avoid_other_parts="True" \
-s skin_line_width="0.4" \
-s wall_line_width_0="0.4" \
-s support_tree_top_rate="10" \
-s interlocking_enable="False" \
-s cross_infill_pocket_size="4.0" \
-s material_end_of_filament_purge_speed="0.5" \
-s fill_outline_gaps="True" \
-s prime_tower_base_height="0.3" \
-s material_maximum_park_duration="300" \
-s switch_extruder_retraction_amount="16" \
-s machine_nozzle_temp_enabled="True" \
-s skirt_brim_speed="37.5" \
-s bridge_wall_speed="15.0" \
-s support_bottom_offset="0.0" \
-s support_interface_pattern="concentric" \
-s bridge_fan_speed_2="0" \
-s initial_bottom_layers="0" \
-s bridge_skin_material_flow_3="110" \
-s machine_nozzle_id="unknown" \
-s speed_equalize_flow_width_factor="100.0" \
-s wipe_brush_pos_x="100" \
-s acceleration_travel_layer_0="3000.0" \
-s raft_surface_monotonic="False" \
-s min_polygon_deceleration_scale="10.0" \
-s flooring_layer_count="0" \
-s jerk_travel="20" \
-s infill_wipe_dist="0.1" \
-s prime_tower_min_volume="6" \
-s support_tree_rest_preference="graceful" \
-s material_bed_temperature="80" \
-s prime_blob_enable="False" \
-s material_brand="empty_brand" \
-s skin_monotonic="False" \
-s meshfix_keep_open_polygons="False" \
-s speed_roofing="30.0" \
-s magic_fuzzy_skin_point_dist="0.8" \
-s bridge_skin_speed_3="15.0" \
-s speed_slowdown_layers="2" \
-s default_material_print_temperature="210" \
-s infill_line_distance="4.0" \
-s minimum_bottom_area="1.0" \
-s retraction_combing_max_distance="0" \
-s draft_shield_height="10" \
-s machine_always_write_active_tool="False" \
-s z_seam_x="0.0" \
-s cool_min_speed="10" \
-s wall0_extra_dist_ratio="1.1" \
-s retraction_hop_after_extruder_switch_height="1" \
-s raft_interface_wall_count="0" \
-s skirt_gap="8" \
-s support_tower_roof_angle="65" \
-s extruder_prime_pos_z="0" \
-s meshfix_fluid_motion_shift_distance="0.1" \
-s support_join_distance="2.0" \
-s wipe_hop_amount="1" \
-s raft_interface_thickness="0.44999999999999996" \
-s machine_head_with_fans_polygon="[[-2, 10], [-2, -10], [10, -10], [-20, -10]]" \
-s z_seam_corner="z_seam_corner_inner" \
-s support_conical_enabled="False" \
-s material_anti_ooze_retraction_speed="5" \
-s max_skin_angle_for_expansion="90" \
-s platform_adhesion="0" \
-s bottom_skin_expand_distance="0.4" \
-s bridge_skin_support_threshold="50" \
-s support_interface_extruder_nr="0" \
-s support_tower_maximum_supported_diameter="3.0" \
-s nozzle_disallowed_areas="[]" \
-s ironing_flow="10.0" \
-s machine_nozzle_cool_down_speed="2.0" \
-s cool_fan_speed_min="100.0" \
-s wipe_move_distance="20" \
-s raft_base_remove_inside_corners="False" \
-s z_seam_on_vertex="False" \
-s adaptive_layer_max_speed_ratio="1.5" \
-s skin_outline_count="1" \
-s jerk_prime_tower="20" \
-s speed_wall_x_flooring="60.0" \
-s support_bottom_pattern="concentric" \
-s infill_extruder_nr="-1" \
-s infill_sparse_density="20" \
-s support_roof_wall_count="0" \
-s support_connect_zigzags="True" \
-s layer_0_after_raft_fan="100" \
-s adaptive_layer_height_enabled="True" \
-s raft_acceleration="3000" \
-s material_is_support_material="False" \
-s seam_overhang_angle="50" \
-s group_outer_walls="True" \
-s jerk_travel_enabled="True" \
-s conical_overhang_hole_size="0" \
-s hole_xy_offset_max_diameter="0" \
-s acceleration_layer_0="3000" \
-s retract_at_layer_change="False" \
-s wall_transition_length="0.4" \
-s wall_0_inset="0" \
-s relative_extrusion="False" \
-s meshfix_maximum_extrusion_area_deviation="50000" \
-s wall_0_material_flow="100" \
-s machine_steps_per_mm_x="50" \
-s prime_tower_position_x="10.0" \
-s experimental="0" \
-s bridge_wall_min_length="2.1" \
-s top_skin_expand_distance="0.4" \
-s support_enable="False" \
-s layer_0_max_flow_acceleration="1" \
-s wall_x_material_flow_flooring="100" \
-s interlocking_beam_layer_count="2" \
-s bridge_fan_speed="100" \
-s support_tree_max_diameter="25" \
-s support_material_flow="100" \
-s min_feature_size="0.1" \
-s support_xy_distance_overhang="0.3" \
-s layer_0_z_overlap="0.07" \
-s raft_interface_margin="4" \
-s ironing_pattern="zigzag" \
-s time="21:16:08" \
-s machine_buildplate_type="glass" \
-s raft_base_jerk="20" \
-s layer_0_after_raft_temp="230.0" \
-s support_interface_priority="interface_area_overwrite_support_area" \
-s wipe_repeat_count="5" \
-s meshfix_maximum_resolution="0.5" \
-s acceleration_infill="3000" \
-s travel_avoid_supports="False" \
-s jerk_print="20" \
-s support_roof_height="1" \
-s machine_steps_per_mm_z="50" \
-s skin_angles="[ ]" \
-s machine_scale_fan_speed_zero_to_one="False" \
-s retraction_extra_prime_amount="0" \
-s wipe_retraction_speed="25" \
-s retraction_amount="6.5" \
-s wall_0_deceleration="20.0" \
-s retraction_speed="25" \
-s prime_tower_line_width="0.4" \
-s acceleration_wall_x_flooring="3000" \
-s jerk_wall_x_roofing="20" \
-s bridge_skin_speed_2="15.0" \
-s material_anti_ooze_retracted_position="-4" \
-s jerk_print_layer_0="20" \
-s hole_xy_offset="0" \
-s bv_temp_warn_limit="7.5" \
-s material_break_temperature="50" \
-s wipe_retraction_amount="6.5" \
-s machine_center_is_zero="True" \
-s machine_end_gcode="M104 S0
M140 S0
;Retract the filament
G92 E1
G1 E-1 F300
G28 X0 Y0
M84" \
-s acceleration_flooring="3000" \
-s acceleration_enabled="False" \
-s support_roof_extruder_nr="0" \
-s jerk_wall_0_flooring="20" \
-s top_thickness="0.8" \
-s flooring_angles="[ ]" \
-s support_type="everywhere" \
-s skin_edge_support_layers="0" \
-s travel_speed="60" \
-s speed="0" \
-s min_infill_area="0" \
-s machine_nozzle_tip_outer_diameter="1" \
-s support_xy_overrides_z="z_overrides_xy" \
-s bridge_skin_material_flow_2="100" \
-s raft_surface_z_offset="0.0" \
-s speed_support_interface="50.0" \
-s machine_max_acceleration_x="9000" \
-s reset_flow_duration="2.0" \
-s switch_extruder_extra_prime_amount="0" \
-s raft_interface_extruder_nr="0" \
-s small_feature_speed_factor="50" \
-s interlocking_orientation="22.5" \
-s raft_speed="37.5" \
-s mold_angle="40" \
-s support_bottom_extruder_nr="0" \
-s jerk_wall_0="20" \
-s retraction_hop="1" \
-s speed_travel_layer_0="30.0" \
-s conical_overhang_enabled="False" \
-s speed_wall="30.0" \
-s support_xy_distance="0.7" \
-s roofing_monotonic="True" \
-s mini_polygon_deceleration_mm="8.0" \
-s bottom_thickness="0.8" \
-s bridge_skin_speed="15.0" \
-s skirt_brim_material_flow="100" \
-s wall_transition_filter_deviation="0.1" \
-s material_break_preparation_temperature="210" \
-s meshfix_fluid_motion_enabled="True" \
-s infill_pattern="grid" \
-s connect_skin_polygons="False" \
-s brim_replaces_support="True" \
-s machine_extruders_share_heater="False" \
-s speed_wall_0="30.0" \
-s cool_lift_head="False" \
-s material_break_speed="25" \
-s print_sequence="all_at_once" \
-s skin_overlap="5" \
-s speed_infill="60" \
-s raft_flow="100.0" \
-s prime_tower_wipe_enabled="True" \
-s machine_extruders_share_nozzle="False" \
-s support_bottom_height="1" \
-s speed_wall_0_roofing="30.0" \
-s bottom_skin_preshrink="0.4" \
-s support_bottom_stair_step_width="5.0" \
-s prime_tower_raft_base_line_spacing="2.4" \
-s quality_changes_name="幸韵纹理2" \
-s machine_nozzle_size="0.4" \
-s flooring_monotonic="True" \
-s support_interface_offset="0.0" \
-s wall0_extra_dir_flip="False" \
-s wall_0_end_speed_ratio="100.0" \
-s raft_surface_acceleration="3000" \
-s acceleration_roofing="3000" \
-s retraction_hop_enabled="False" \
-s raft_surface_extruder_nr="0" \
-s switch_extruder_retraction_speed="20" \
-s top_skin_preshrink="0.4" \
-s small_hole_max_size="0" \
-s raft_interface_flow="100.0" \
-s line_width="0.4" \
-s optimize_wall_printing_order="True" \
-s zig_zaggify_support="False" \
-s wipe_hop_enable="False" \
-s scarf_joint_seam_start_height_ratio="0" \
-s z_seam_position="back" \
-s small_feature_max_length="0.0" \
-s skin_overlap_mm="0.02" \
-s wall_0_material_flow_flooring="100" \
-s prime_tower_position_y="474.17499999999995" \
-s mesh_position_x="0" \
-s expand_skins_expand_distance="0.4" \
-s user_speed_ratio_definition_enable="False" \
-s bridge_wall_material_flow="50" \
-s wall_0_acceleration="20.0" \
-s raft_interface_smoothing="5" \
-s support_interface_angles="[ ]" \
-s jerk_wall_x_flooring="20" \
-s material_diameter="2.85" \
-s flow_rate_max_extrusion_offset="0" \
-s skirt_brim_minimal_length="250" \
-s raft_surface_layers="2" \
-s machine_disallowed_areas="[]" \
-s speed_layer_0="30.0" \
-s raft_surface_speed="37.5" \
-s support_roof_line_distance="0.8" \
-s brim_line_count="10" \
-s material_shrinkage_percentage_z="100.0" \
-s material_guid="" \
-s zig_zaggify_infill="False" \
-s retraction_combing_avoid_distance="0.6000000000000001" \
-s machine_max_acceleration_e="10000" \
-s use_user_defined_seam="False" \
-s raft_base_thickness="0.36" \
-s jerk_support="20" \
-s wall_line_width_x="0.4" \
-s retraction_hop_after_extruder_switch="True" \
-s support_top_distance="0.1" \
-s machine_acceleration="4000" \
-s material_type="empty" \
-s support_zag_skip_count="4" \
-s draw_z_seam_enable="False" \
-s brim_inside_margin="3.2" \
-s wipe_retraction_extra_prime_amount="0" \
-s machine_shape="elliptic" \
-s machine_heated_bed="False" \
-s machine_use_extruder_offset_to_offset_coords="False" \
-s jerk_ironing="20" \
-s machine_gcode_flavor="RepRap (Marlin/Sprinter)" \
-s mold_roof_height="0.5" \
-s flow_warn_limit="15.0" \
-s wall_overhang_speed_factors="[100]" \
-s material_flow_temp_graph="[[3.5, 200],[7.0, 240]]" \
-s support_z_seam_min_distance="0.8" \
-s machine_start_gcode_first="False" \
-s infill_before_walls="True" \
-s material="0" \
-s material_bed_temp_prepend="True" \
-s adhesion_type="raft" \
-s machine_settings="0" \
-s acceleration_topbottom="3000" \
-s build_fan_full_layer="1" \
-s travel_retract_before_outer_wall="False" \
-s machine_height="1800.0" \
-s prime_tower_base_size="4" \
-s wipe_retraction_prime_speed="25" \
-s wipe_pause="0" \
-s machine_nozzle_expansion_angle="45" \
-s min_odd_wall_line_width="0.34" \
-s flooring_material_flow="100" \
-s machine_max_feedrate_e="************" \
-s raft_remove_inside_corners="False" \
-s skirt_line_count="1" \
-s material_id="empty_material" \
-s retraction_count_max="90" \
-s jerk_infill="20" \
-s nozzle_offsetting_for_disallowed_areas="True" \
-s acceleration_skirt_brim="3000" \
-s meshfix="0" \
-s cool_fan_speed="100.0" \
-s raft_surface_thickness="0.3" \
-s support_conical_min_width="5.0" \
-s interlocking_beam_width="0.8" \
-s support_tree_branch_diameter_angle="7" \
-s support_mesh_drop_down="True" \
-s machine_max_jerk_z="0.4" \
-s support_tree_angle="50" \
-s prime_tower_size="20" \
-s brim_width="8.0" \
-s skin_preshrink="0.4" \
-s magic_fuzzy_skin_outside_only="False" \
-s support_tree_bp_diameter="7.5" \
-s infill_line_width="0.4" \
-s support_z_seam_away_from_model="True" \
-s raft_smoothing="5" \
-s lightning_infill_straightening_angle="40" \
-s speed_topbottom="30.0" \
-s anti_overhang_mesh="False" \
-s infill_material_flow="100" \
-s retraction_extrusion_window="6.5" \
-s support_fan_enable="False" \
-s center_object="False" \
-s skirt_brim_line_width="0.4" \
-s cool_fan_full_layer="1" \
-s machine_max_feedrate_z="************" \
-s support_tree_max_diameter_increase_by_merges_when_support_to_model="1" \
-s material_print_temp_prepend="True" \
-s machine_heat_zone_length="16" \
-s jerk_skirt_brim="20" \
-s slicing_tolerance="middle" \
-s skirt_height="1" \
-s support_line_distance="5.333333333333333" \
-s retraction_enable="True" \
-s dual="0" \
-s support_bottom_enable="False" \
-s machine_name="Unknown" \
-s meshfix_fluid_motion_small_distance="0.01" \
-s material_pressure_advance_factor="0.05" \
-s cool_fan_speed_0="0" \
-s infill_support_enabled="False" \
-s blackmagic="0" \
-s support_brim_line_count="3" \
-s raft_surface_margin="4" \
-s minimum_roof_area="1.0" \
-s material_shrinkage_percentage_xy="100.0" \
-s infill_enable_travel_optimization="False" \
-s cool_min_layer_time_overhang_min_segment_length="5" \
-s infill="0" \
-s support_infill_extruder_nr="0" \
-s prime_tower_flow="100" \
-s acceleration_ironing="3000" \
-s raft_base_fan_speed="0" \
-s coasting_speed="90" \
-s raft_base_flow="100.0" \
-s meshfix_union_all="True" \
-s support_z_distance="0.1" \
-s support_initial_layer_line_distance="5.333333333333333" \
-s bridge_enable_more_layers="True" \
-s acceleration_support="3000" \
-s jerk_travel_layer_0="20.0" \
-s gradual_flow_enabled="False" \
-s bridge_sparse_infill_max_density="0" \
-s infill_mesh="False" \
-s material_break_preparation_retracted_position="-16" \
-s layer_height="0.3" \
-s machine_endstop_positive_direction_y="False" \
-s wall_0_material_flow_roofing="100" \
-s machine_extruder_count="1" \
-s jerk_support_roof="20" \
-s cool_fan_full_at_height="0" \
-s support_brim_enable="True" \
-s bridge_skin_density_2="75" \
-s support_infill_density_multiplier_initial_layer="1" \
-s raft_interface_fan_speed="0" \
-s support_bottom_stair_step_min_slope="10.0" \
-s default_material_bed_temperature="80" \
-s wall_0_speed_split_distance="1.0" \
-s wall_x_material_flow_layer_0="100" \
-s speed_print_layer_0="30.0" \
-s raft_jerk="20" \
-s acceleration_travel_enabled="True" \
-s support_interface_material_flow="100" \
-s infill_overlap="10" \
-s prime_tower_base_curve_magnitude="4" \
-s extruder_prime_pos_abs="False" \
-s material_adhesion_tendency="10" \
-s machine_extruders_shared_nozzle_initial_retraction="0" \
-s gantry_height="1800.0" \
-s material_surface_energy="100" \
-s wall_x_material_flow="100" \
-s raft_base_smoothing="5" \
-s connect_infill_polygons="False" \
-s wall_material_flow="100" \
-s retraction_combing="all" \
-s material_flow_layer_0="100" \
-s meshfix_union_all_remove_holes="True" \
-s jerk_wall_0_roofing="20" \
-e0 \
-s extruder_nr="0" \
-l ../../tests/testModel.stl \
-o test_spiralize_complete.gcode 2>&1

echo ""
echo "检查是否生成了G-code文件:"
if [ -f "test_spiralize_simple.gcode" ]; then
    echo "✅ G-code文件已生成: test_spiralize_simple.gcode"
    echo "文件大小: $(wc -c < test_spiralize_simple.gcode) 字节"
else
    echo "❌ G-code文件未生成"
fi

echo ""
echo "=== 测试完成 ==="
