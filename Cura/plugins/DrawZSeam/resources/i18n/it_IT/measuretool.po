# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the Measure Tool package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: Measure Tool\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-24 16:13+0000\n"
"PO-Revision-Date: 2023-03-27 21:07+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.1.1\n"

#: __init__.py:26 MeasureTool.py:170
msgctxt "@label"
msgid "Measure"
msgstr "Misura"

#: __init__.py:28
msgctxt "@info:tooltip"
msgid "Measure parts of objects."
msgstr "Misura le parti degli oggetti."

#: resources/qml/MeasureTool.qml:82
msgctxt "@action:button"
msgid "Toggle fixing the 'From' point in place"
msgstr "Attiva/disattiva la correzione del punto 'Da' sul posto"

#: resources/qml/MeasureTool.qml:90 resources/qml_qt5/MeasureTool.qml:65
msgctxt "@label"
msgid "From"
msgstr "Da"

#: resources/qml/MeasureTool.qml:101 resources/qml_qt5/MeasureTool.qml:76
msgctxt "@label"
msgid "To"
msgstr "A"

#: resources/qml/MeasureTool.qml:112 resources/qml_qt5/MeasureTool.qml:87
msgctxt "@label"
msgid "Distance"
msgstr "Distanza"

#: resources/qml/MeasureTool.qml:232 resources/qml_qt5/MeasureTool.qml:223
msgctxt "@label"
msgid "Diagonal"
msgstr "Diagonale"

#: resources/qml/MeasureTool.qml:254
msgctxt "@action:button"
msgid "Reset"
msgstr "Resetta"

#: resources/qml/MeasureTool.qml:269 resources/qml_qt5/MeasureTool.qml:246
msgctxt "@label"
msgid "Unit"
msgstr "Unità"

#: resources/qml/MeasureTool.qml:279 resources/qml_qt5/MeasureTool.qml:258
msgctxt "@option:unit"
msgid "Micron"
msgstr "Micron"

#: resources/qml/MeasureTool.qml:280 resources/qml_qt5/MeasureTool.qml:259
msgctxt "@option:unit"
msgid "Millimeter (default)"
msgstr "Millimetri (default)"

#: resources/qml/MeasureTool.qml:281 resources/qml_qt5/MeasureTool.qml:260
msgctxt "@option:unit"
msgid "Centimeter"
msgstr "Centimetri"

#: resources/qml/MeasureTool.qml:282 resources/qml_qt5/MeasureTool.qml:261
msgctxt "@option:unit"
msgid "Meter"
msgstr "Metri"

#: resources/qml/MeasureTool.qml:283 resources/qml_qt5/MeasureTool.qml:262
msgctxt "@option:unit"
msgid "Inch"
msgstr "Pollici"

#: resources/qml/MeasureTool.qml:284 resources/qml_qt5/MeasureTool.qml:263
msgctxt "@option:unit"
msgid "Feet"
msgstr "Piedi"

#: resources/qml/MeasureTool.qml:324 resources/qml_qt5/MeasureTool.qml:310
msgctxt "@option:check"
msgid "Snap to model points"
msgstr "Aggancia ai punti del modello"
