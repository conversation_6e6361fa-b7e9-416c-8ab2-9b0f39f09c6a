# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the Measure Tool package.
# <AUTHOR> <EMAIL>, 2023.
msgid ""
msgstr ""
"Project-Id-Version: Measure Tool\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-24 16:13+0000\n"
"PO-Revision-Date: 2023-03-28 11:28+0200\n"
"Last-Translator: 5axes <<EMAIL>>\n"
"Language-Team: 5axes\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.2.2\n"

#: __init__.py:26 MeasureTool.py:170
msgctxt "@label"
msgid "Measure"
msgstr "Mesure"

#: __init__.py:28
msgctxt "@info:tooltip"
msgid "Measure parts of objects."
msgstr "Mesurer des dimmensions d'objets."

#: resources/qml/MeasureTool.qml:82
msgctxt "@action:button"
msgid "Toggle fixing the 'From' point in place"
msgstr "Activer/désactiver fixation en place du point \"De\""

#: resources/qml/MeasureTool.qml:90 resources/qml_qt5/MeasureTool.qml:65
msgctxt "@label"
msgid "From"
msgstr "De"

#: resources/qml/MeasureTool.qml:101 resources/qml_qt5/MeasureTool.qml:76
msgctxt "@label"
msgid "To"
msgstr "A"

#: resources/qml/MeasureTool.qml:112 resources/qml_qt5/MeasureTool.qml:87
msgctxt "@label"
msgid "Distance"
msgstr "Distance"

#: resources/qml/MeasureTool.qml:232 resources/qml_qt5/MeasureTool.qml:223
msgctxt "@label"
msgid "Diagonal"
msgstr "Diagonale"

#: resources/qml/MeasureTool.qml:254
msgctxt "@action:button"
msgid "Reset"
msgstr "Réinitialiser"

#: resources/qml/MeasureTool.qml:269 resources/qml_qt5/MeasureTool.qml:246
msgctxt "@label"
msgid "Unit"
msgstr "Unité"

#: resources/qml/MeasureTool.qml:279 resources/qml_qt5/MeasureTool.qml:258
msgctxt "@option:unit"
msgid "Micron"
msgstr "Micron"

#: resources/qml/MeasureTool.qml:280 resources/qml_qt5/MeasureTool.qml:259
msgctxt "@option:unit"
msgid "Millimeter (default)"
msgstr "Millimètre (défaut)"

#: resources/qml/MeasureTool.qml:281 resources/qml_qt5/MeasureTool.qml:260
msgctxt "@option:unit"
msgid "Centimeter"
msgstr "Centimètre"

#: resources/qml/MeasureTool.qml:282 resources/qml_qt5/MeasureTool.qml:261
msgctxt "@option:unit"
msgid "Meter"
msgstr "Mètre"

#: resources/qml/MeasureTool.qml:283 resources/qml_qt5/MeasureTool.qml:262
msgctxt "@option:unit"
msgid "Inch"
msgstr "Pouce"

#: resources/qml/MeasureTool.qml:284 resources/qml_qt5/MeasureTool.qml:263
msgctxt "@option:unit"
msgid "Feet"
msgstr "Pied"

#: resources/qml/MeasureTool.qml:324 resources/qml_qt5/MeasureTool.qml:310
msgctxt "@option:check"
msgid "Snap to model points"
msgstr "Accrochage aux points du modèle"
