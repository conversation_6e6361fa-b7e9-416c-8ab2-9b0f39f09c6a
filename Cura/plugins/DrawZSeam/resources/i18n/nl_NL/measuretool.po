# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the Measure Tool package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: Measure Tool\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-24 16:13+0000\n"
"PO-Revision-Date: 2023-03-28 11:22+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: nl_NL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.2.2\n"

#: __init__.py:26 MeasureTool.py:170
msgctxt "@label"
msgid "Measure"
msgstr "Meet"

#: __init__.py:28
msgctxt "@info:tooltip"
msgid "Measure parts of objects."
msgstr "Meet delen van objecten."

#: resources/qml/MeasureTool.qml:82
msgctxt "@action:button"
msgid "Toggle fixing the 'From' point in place"
msgstr "Zet het \"Van\" punt vast of los"

#: resources/qml/MeasureTool.qml:90 resources/qml_qt5/MeasureTool.qml:65
msgctxt "@label"
msgid "From"
msgstr "Van"

#: resources/qml/MeasureTool.qml:101 resources/qml_qt5/MeasureTool.qml:76
msgctxt "@label"
msgid "To"
msgstr "Naar"

#: resources/qml/MeasureTool.qml:112 resources/qml_qt5/MeasureTool.qml:87
msgctxt "@label"
msgid "Distance"
msgstr "Afstand"

#: resources/qml/MeasureTool.qml:232 resources/qml_qt5/MeasureTool.qml:223
msgctxt "@label"
msgid "Diagonal"
msgstr "Diagonaal"

#: resources/qml/MeasureTool.qml:254
msgctxt "@action:button"
msgid "Reset"
msgstr "Herstellen"

#: resources/qml/MeasureTool.qml:269 resources/qml_qt5/MeasureTool.qml:246
msgctxt "@label"
msgid "Unit"
msgstr "Eenheid"

#: resources/qml/MeasureTool.qml:279 resources/qml_qt5/MeasureTool.qml:258
msgctxt "@option:unit"
msgid "Micron"
msgstr "Micrometer"

#: resources/qml/MeasureTool.qml:280 resources/qml_qt5/MeasureTool.qml:259
msgctxt "@option:unit"
msgid "Millimeter (default)"
msgstr "Milimeter (standaard)"

#: resources/qml/MeasureTool.qml:281 resources/qml_qt5/MeasureTool.qml:260
msgctxt "@option:unit"
msgid "Centimeter"
msgstr "Centimeter"

#: resources/qml/MeasureTool.qml:282 resources/qml_qt5/MeasureTool.qml:261
msgctxt "@option:unit"
msgid "Meter"
msgstr "Meter"

#: resources/qml/MeasureTool.qml:283 resources/qml_qt5/MeasureTool.qml:262
msgctxt "@option:unit"
msgid "Inch"
msgstr "Duim"

#: resources/qml/MeasureTool.qml:284 resources/qml_qt5/MeasureTool.qml:263
msgctxt "@option:unit"
msgid "Feet"
msgstr "Voet"

#: resources/qml/MeasureTool.qml:324 resources/qml_qt5/MeasureTool.qml:310
msgctxt "@option:check"
msgid "Snap to model points"
msgstr "Snap naar hoekpunten"
